package com.wonderslate_v2

import org.springframework.security.access.annotation.Secured
import com.wonderslate_v2.usermanagement.User

import javax.transaction.Transactional

class HomeController {

    @Secured(['ROLE_USER'])
    def index() {
        println(session.user)
        [user: session.user]
    }


    @Transactional
    def store(){
        println(session.user)
        [user: session.user]
        def User = User.findByUsername(session.user.username)
        println(User)
    }
}
